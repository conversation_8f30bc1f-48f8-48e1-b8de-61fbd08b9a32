import { Component, Event, EventEmitter, State, Listen, Prop, h } from '@stencil/core';
import {
  RespondentDetailOption,
  AcceptedInputTypes,
} from '../../../../global/script/var/RespondentDetailsOptions';
import { SelectOption } from '../../../../global/script/interfaces';
import { FrontendLogger } from '../../../../global/script/var';
import { validateRespondentDetailPayload } from './helpers';

/**
 * Component for creating and editing custom respondent details
 */
@Component({
  tag: 'p-respondent-detail-form',
  styleUrl: 'p-respondent-detail-form.css',
  shadow: true,
})
export class PRespondentDetailForm {
  @Event({
    eventName: 'addCustomRespondentDetail',
    bubbles: true,
  })
  addCustomRespondentDetailEventEmitter: EventEmitter;

  @Prop() editingDetail: string = '';
  @Prop() isEditMode: boolean = false;

  @State() detailValue: string = '';
  @State() detailLabel: string = '';
  @State() detailInputType: string = 'text';
  @State() detailPlaceholder: string = '';
  @State() detailOptions: SelectOption[] = [];
  @State() detailRequired: boolean = true;
  @State() currentOption: string = '';
  @State() showOptionsSection: boolean = false;
  @State() formErrors: { [key: string]: string } = {};

  componentDidLoad() {
    FrontendLogger.debug('p-respondent-detail-form componentDidLoad called');
    FrontendLogger.debug('Initial state:', {
      detailLabel: this.detailLabel,
      detailInputType: this.detailInputType,
      detailPlaceholder: this.detailPlaceholder,
      editingDetail: this.editingDetail,
      isEditMode: this.isEditMode,
    });

    if (this.editingDetail && this.isEditMode) {
      this.populateFormForEditing(this.editingDetail);
    }
  }

  private populateFormForEditing(detailJson: string) {
    try {
      const detail: RespondentDetailOption = JSON.parse(detailJson);

      this.detailValue = detail.value || '';
      this.detailLabel = detail.label || '';
      this.detailInputType = detail.inputType || 'text';
      this.detailRequired = detail.required !== undefined ? detail.required : true;
      this.detailPlaceholder = detail.placeholder || '';
      this.detailOptions = detail.options || [];
      this.showOptionsSection = ['dropdown', 'radio', 'checkbox'].includes(this.detailInputType);

      this.formErrors = {};
    } catch (error) {
      FrontendLogger.error('Error parsing editing detail:', error);
    }
  }

  @Listen('inputEvent')
  handleInputEvent(event: CustomEvent) {
    const { name, value } = event.detail;
    FrontendLogger.debug('Input event:', { name, value });

    if (name === 'detailLabel') {
      this.detailLabel = value;
    } else if (name === 'detailPlaceholder') {
      this.detailPlaceholder = value;
    } else if (name === 'currentOption') {
      this.currentOption = value;
    }

    // Always validate the entire form after any input change
    this.validateForm();
  }

  @Listen('selectChangeEvent')
  handleSelectChangeEvent(event: CustomEvent) {
    if (event.detail.name === 'detailInputType') {
      FrontendLogger.debug('Input type changed to:', event.detail.value);
      this.detailInputType = event.detail.value;
      this.showOptionsSection = ['dropdown', 'radio', 'checkbox'].includes(this.detailInputType);

      // Clear options if switching away from a type that uses options
      if (!this.showOptionsSection) {
        this.detailOptions = [];
      }
    }

    // Always validate the entire form after any select change
    this.validateForm();
  }

  @Listen('buttonClickEvent')
  handleButtonClickEvent(event: CustomEvent) {
    if (event.detail.action === 'addOption') {
      this.addOption();
    } else if (event.detail.action === 'addRespondentDetail') {
      this.addRespondentDetail();
    }
  }

  @Listen('listWithDeleteEvent')
  handleListWithDeleteEvent(event: CustomEvent) {
    if (event.detail.name === 'deleteOption') {
      this.removeOption(event.detail.value);
    }
  }

  private addOption() {
    if (!this.currentOption.trim()) return;

    // Create a value from the label (lowercase, replace spaces with underscores)
    const optionValue = this.currentOption
      .trim()
      .toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^a-z0-9_]/g, '');

    // Check if this value already exists
    const optionExists = this.detailOptions.some(option => option.value === optionValue);
    if (optionExists) return;

    const newOption: SelectOption = {
      value: optionValue,
      label: this.currentOption.trim(),
    };

    this.detailOptions = [...this.detailOptions, newOption];
    this.currentOption = '';
    this.validateForm();
  }

  private removeOption(value: string) {
    this.detailOptions = this.detailOptions.filter(option => option.value !== value);

    this.validateForm();
  }

  private validateForm(fieldName?: string): boolean {
    FrontendLogger.debug('validateForm called with state:', {
      detailLabel: this.detailLabel,
      detailInputType: this.detailInputType,
      detailPlaceholder: this.detailPlaceholder,
      showOptionsSection: this.showOptionsSection,
      detailOptions: this.detailOptions,
      formErrors: this.formErrors,
    });

    const validationData = {
      label: this.detailLabel,
      inputType: this.detailInputType,
      placeholder: this.detailPlaceholder,
      options: this.detailOptions,
      required: this.detailRequired,
    };

    const { isValid, errors } = validateRespondentDetailPayload(validationData);

    // Update form errors with validation results
    this.formErrors = errors;

    if (!isValid) {
      FrontendLogger.debug('Validation failed with errors:', this.formErrors);

      // If validating a specific field, return whether that field is valid
      if (fieldName) {
        return !this.formErrors[fieldName];
      }
      return false;
    }

    FrontendLogger.debug('Validation passed');
    return true;
  }

  private isFormValid(): boolean {
    const validationData = {
      label: this.detailLabel,
      inputType: this.detailInputType,
      placeholder: this.detailPlaceholder,
      options: this.detailOptions,
      required: this.detailRequired,
    };

    const { isValid } = validateRespondentDetailPayload(validationData);
    return isValid;
  }

  public addRespondentDetail() {
    FrontendLogger.debug('addRespondentDetail called');
    FrontendLogger.debug('Form state:', {
      detailLabel: this.detailLabel,
      detailInputType: this.detailInputType,
      detailPlaceholder: this.detailPlaceholder,
      showOptionsSection: this.showOptionsSection,
      detailOptions: this.detailOptions,
    });

    const isValid = this.validateForm();
    FrontendLogger.debug('Form validation result:', isValid);

    if (!isValid) {
      FrontendLogger.debug('Form validation failed, not submitting');
      return;
    }

    let detailValue: string;

    if (this.isEditMode && this.editingDetail) {
      // In edit mode, preserve the original value
      try {
        const originalDetail: RespondentDetailOption = JSON.parse(this.editingDetail);
        detailValue = originalDetail.value;
      } catch (error) {
        FrontendLogger.error('Error parsing original detail value:', error);
        // Fallback to generating new value
        detailValue = this.detailLabel
          .trim()
          .toLowerCase()
          .replace(/\s+/g, '_')
          .replace(/[^a-z0-9_]/g, '');
      }
    } else {
      // Generate a unique value for the detail based on the label
      detailValue = this.detailLabel
        .trim()
        .toLowerCase()
        .replace(/\s+/g, '_')
        .replace(/[^a-z0-9_]/g, '');
    }

    const respondentDetail: RespondentDetailOption = {
      value: detailValue,
      label: this.detailLabel.trim(),
      inputType: this.detailInputType,
      required: this.isEditMode ? this.detailRequired : true, // Preserve original required value in edit mode
      placeholder: this.detailPlaceholder.trim() || undefined,
      options: this.showOptionsSection ? [...this.detailOptions] : undefined,
    };

    FrontendLogger.debug('Emitting addCustomRespondentDetail with:', respondentDetail);

    this.addCustomRespondentDetailEventEmitter.emit({
      respondentDetail: respondentDetail,
    });
  }

  private formatOptionsForList() {
    return JSON.stringify(
      this.detailOptions.map(option => ({
        value: option.value,
        label: option.label,
      })),
    );
  }

  render() {
    return (
      <div class="respondent-detail-form">
        <e-text>
          <strong>
            Label <span class="mandatory"> * </span>
          </strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-input
          type="text"
          name="detailLabel"
          placeholder="e.g. Favorite Color"
          value={this.detailLabel}
        ></e-input>
        {this.formErrors.label && (
          <div class="error-message">
            <e-text variant="footnote">{this.formErrors.label}</e-text>
          </div>
        )}
        <l-spacer value={2}></l-spacer>

        {/* Input Type Field */}
        <e-text>
          <strong>
            Input Type <span class="mandatory"> * </span>
          </strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-select name="detailInputType" options={JSON.stringify(AcceptedInputTypes)}></e-select>
        <l-spacer value={2}></l-spacer>

        {/* Placeholder Field - only for text, email, and number input types */}
        {['text', 'email', 'number'].includes(this.detailInputType) && (
          <div>
            <e-text>
              <strong>
                {this.detailInputType.toUpperCase()} Placeholder Text{' '}
                <span class="mandatory"> * </span>
              </strong>
            </e-text>
            <l-spacer value={0.5}></l-spacer>
            <e-input
              type="text"
              name="detailPlaceholder"
              placeholder="e.g. Enter your favorite color"
              value={this.detailPlaceholder}
            ></e-input>
            {this.formErrors.placeholder && (
              <div class="error-message">
                <e-text variant="footnote">{this.formErrors.placeholder}</e-text>
              </div>
            )}
            <l-spacer value={2}></l-spacer>
          </div>
        )}

        {/* Options Section (only for select, radio, checkbox) */}
        {this.showOptionsSection && (
          <div>
            <e-text>
              <strong>
                {this.detailInputType.toUpperCase()} Options <span class="mandatory"> * </span>
              </strong>
            </e-text>
            <l-spacer value={0.5}></l-spacer>
            <l-row justifyContent="flex-start">
              <e-input
                type="text"
                name="currentOption"
                placeholder="e.g. Red"
                value={this.currentOption}
              ></e-input>
              <l-spacer variant="horizontal" value={0.5}></l-spacer>
              <e-button variant="ghost" action="addOption" disabled={!this.currentOption.trim()}>
                Add
              </e-button>
            </l-row>
            <l-spacer value={0.5}></l-spacer>
            <p-list-with-delete
              name="deleteOption"
              items={this.formatOptionsForList()}
              emptyMessage="No options added yet"
            ></p-list-with-delete>
            {this.formErrors.options && (
              <div class="error-message">
                <e-text variant="footnote">{this.formErrors.options}</e-text>
              </div>
            )}
            <l-spacer value={1.5}></l-spacer>
          </div>
        )}

        {/* Submit Button */}
        <l-spacer value={2}></l-spacer>
        <l-row justifyContent="flex-end">
          <e-button action="addRespondentDetail" disabled={!this.isFormValid()}>
            {this.isEditMode ? 'Update' : 'Add'}
          </e-button>
        </l-row>
      </div>
    );
  }
}
