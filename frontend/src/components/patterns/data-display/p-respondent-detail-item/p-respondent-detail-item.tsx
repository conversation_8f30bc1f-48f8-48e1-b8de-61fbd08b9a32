import { Component, Event, EventEmitter, Prop, State, Watch, h } from '@stencil/core';
import { RespondentDetailOption } from '../../../../global/script/var/RespondentDetailsOptions';

/**
 * Component for displaying a respondent detail item with edit and delete options
 */
@Component({
  tag: 'p-respondent-detail-item',
  styleUrl: 'p-respondent-detail-item.css',
  shadow: true,
})
export class PRespondentDetailItem {
  @Event({
    eventName: 'respondentDetailDeleteEvent',
    bubbles: true,
  })
  respondentDetailDeleteEventEmitter: EventEmitter;

  @Event({
    eventName: 'respondentDetailEditEvent',
    bubbles: true,
  })
  respondentDetailEditEventEmitter: EventEmitter;

  @Prop() detail: string;
  @Prop() index: number;

  @State() detailObj: RespondentDetailOption;

  @Watch('detail')
  watchDetailProp(newValue: string) {
    if (newValue) {
      this.detailObj = JSON.parse(newValue);
    }
  }

  componentWillLoad() {
    if (this.detail) {
      this.detailObj = JSON.parse(this.detail);
    }
  }

  private handleDelete() {
    this.respondentDetailDeleteEventEmitter.emit({
      value: this.detailObj.value,
      index: this.index,
    });
  }

  private handleEdit() {
    this.respondentDetailEditEventEmitter.emit({
      detail: this.detailObj,
      index: this.index,
    });
  }

  private renderOptionsPreview() {
    if (!this.detailObj.options || this.detailObj.options.length === 0) {
      return null;
    }

    return (
      <div class="options-preview">
        <e-text variant="footnote" class="detail-label">
          {this.detailObj.inputType.toUpperCase()} OPTIONS
        </e-text>
        <ul class="options-list">
          {this.detailObj.options.map(option => (
            <li class="option-item">
              <e-text>{option.label}</e-text>
            </li>
          ))}
        </ul>
      </div>
    );
  }

  render() {
    if (!this.detailObj) {
      return null;
    }

    return (
      <li class="detail-item">
        <l-row justifyContent="space-between" align="baseline">
          <div>
            <e-text>
              <strong>{this.detailObj.label}</strong>
              {this.detailObj.required && <span class="required-badge"> * </span>}
            </e-text>
          </div>
          <div class="detail-actions">
            <e-button variant="link" onClick={() => this.handleEdit()}>
              <e-image src="../../../assets/icon/dark/edit-dark.svg" width="1.2em"></e-image>
            </e-button>
            <e-button variant="link" onClick={() => this.handleDelete()}>
              <e-image src="../../../assets/icon/red/trash-red.svg" width="1.2em"></e-image>
            </e-button>
          </div>
        </l-row>
        {(this.detailObj.placeholder ||
          (this.detailObj.options && this.detailObj.options.length > 0)) && (
          <div class="detail-content">
            <div class="detail-info">
              {this.detailObj.placeholder && (
                <div>
                  <l-spacer value={2}></l-spacer>
                  <div class="detail-section">
                    <e-text variant="footnote" class="detail-label">
                      {this.detailObj.inputType.toUpperCase()} PLACEHOLDER
                    </e-text>
                    <e-text>{this.detailObj.placeholder}</e-text>
                  </div>
                </div>
              )}
              {this.detailObj.options && this.detailObj.options.length > 0 && (
                <div>
                  <l-spacer value={2}></l-spacer>
                  {this.renderOptionsPreview()}
                </div>
              )}
            </div>
          </div>
        )}
      </li>
    );
  }
}
