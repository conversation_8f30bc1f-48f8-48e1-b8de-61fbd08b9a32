:host {
  display: block;
  width: 100vw;
  height: 100vh;
  overflow: auto;
  background: white;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
}

main {
  width: 90%;
  max-width: 420px;
  margin: 0 auto;
  margin-top: 4em;
  padding-bottom: 20em;
}

.adjustment-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.row {
  display: flex;
  justify-content: space-between;
}

.row--reverse {
  flex-direction: row-reverse;
}

.survey-radio {
  display: block;
  border-radius: var(--border-radius);
  border: 1px solid var(--color__grey--200);
  padding: calc(var(--padding) * 1.25);
  margin-bottom: 1.5em;
  transition: all 0.25s;
  background: white;
}

.survey-radio:hover {
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Survey Type Specific Hover Styles */
.survey-radio[data-color='purple']:hover {
  border: 1px solid #6a1b9a;
  /* background: #faf5fb; */
}

.survey-radio[data-color='blue']:hover {
  border: 1px solid #3a8dff;
  /* background: #f0f7ff; */
}

.survey-radio[data-color='indigo']:hover {
  border: 1px solid #7c4dff;
  /* background: #f5f2ff; */
}

.survey-radio[data-color='turquoise']:hover {
  border: 1px solid #26c6da;
  /* background: #f0fcfd; */
}

.survey-radio[data-color='green']:hover {
  border: 1px solid #2e7d32;
  /* background: #f4faf4; */
}

.survey-radio[data-color='teal']:hover {
  border: 1px solid #009688;
  /* background: #f0fdf9; */
}

.survey-radio--active {
  color: var(--color__grey--800);
  border: 1px solid var(--color__grey--800);
  background: var(--color__grey--50);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Survey Type Specific Active Styles */
.survey-radio--active[data-color='purple'] {
  border: 1px solid #6a1b9a;
  background: #faf5fb;
}

.survey-radio--active[data-color='blue'] {
  border: 1px solid #3a8dff;
  background: #f0f7ff;
}

.survey-radio--active[data-color='indigo'] {
  border: 1px solid #7c4dff;
  background: #f5f2ff;
}

.survey-radio--active[data-color='turquoise'] {
  border: 1px solid #26c6da;
  background: #f0fcfd;
}

.survey-radio--active[data-color='green'] {
  border: 1px solid #2e7d32;
  background: #f4faf4;
}

.survey-radio--active[data-color='teal'] {
  border: 1px solid #009688;
  background: #f0fdf9;
}

.gallery {
  margin-top: 1.5em;
  display: flex;
  flex-wrap: wrap;
  gap: 1em;
}

.mandatory {
  color: var(--color__red--400);
}

.respondent-attributes {
  margin: 0;
  padding-left: 0em;
  list-style-position: inside;
}

/*
Radio Input
*/

.radio-container {
  display: flex;
  gap: 1em;
  font-family: sans-serif;
}

.survey-preview-options-list {
  padding: 0;
  list-style-position: inside;
  margin: 0;
}

.notice {
  border: 1px solid var(--color__grey--300);
  border-radius: var(--border-radius);
  padding: calc(var(--padding) * 1.5);
  background: var(--color__yellow--50);
}

.notice ul {
  margin: 0;
  padding: 0;
  list-style-position: inside;
}

.respondent-details-list {
  list-style-type: none;
  padding: 0;
  margin: 0.5rem 0 0 0;
  overflow: hidden;
}

.detail-actions {
  display: flex;
  gap: 0.5rem;
}

.optional-star {
  color: var(--color__red--600);
  margin-left: 0.25rem;
  font-weight: bold;
}

.detail-section {
  margin-bottom: 2em;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: var(--color__grey--700);
  font-weight: 600;
  display: block;
}

.options-list {
  list-style-type: none;
  margin: 0.5rem 0 0 0;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.option-item {
  margin-bottom: 0;
  padding: 0.5rem 1rem;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 1.5rem;
  background-color: rgba(0, 0, 0, 0.02);
  display: inline-block;
}

.option-item:last-child {
  margin-bottom: 0;
}

.required-badge {
  color: var(--color__red--600);
  font-size: 0.85em;
}

.detail-options {
  color: var(--color__grey--600);
  font-size: 0.85em;
}

/* Survey Title Colors */
.survey-title--purple {
  color: #4a148c;
}

.survey-title--blue {
  color: #1976d2;
}

.survey-title--indigo {
  color: #512da8;
}

.survey-title--turquoise {
  color: #0097a7;
}

.survey-title--green {
  color: #2e7d32;
}

.survey-title--teal {
  color: #00695c;
}
